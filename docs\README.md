# Smithian Price Watcher 🧐 🌾

**Live-Data meets *The Wealth of Nations*** — explore <PERSON>’s
“market vs. natural price” idea with a Tkinter GUI or a CLI.

```bash
# GUI
smithian-gui

# Command-line, simulated data
smithian-cli --commodity GOLD --window 20


# Smithian Price Watcher 🧐📈  
*“Live-data meets **The Wealth of Nations*** – a tiny but extensible Python workbench for exploring how real-world prices wander around Adam Smith’s _natural price_.”

[![CI](https://github.com/**your-org**/smithian-price-watcher/actions/workflows/ci.yml/badge.svg)](../../actions)  
[![MIT licensed](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)

---

## 0 . Quick demo

```bash
# install once in a fresh venv
pip install -r requirements.txt

# run the GUI – paste any Yahoo/ETF ticker
smithian-gui         

# or use the CLI
smithian-cli GC=F --plot     # gold futures → table + chart PNG

# run tests & lint
pytest
pre-commit run --all-files
1 . What this project does
Feature	Detail
Live price retrieval	Grabs daily closes from Yahoo Finance (via yfinance) for any ticker or commodity symbol (e.g. BTC-USD, CL=F, GLD). Falls back to simulated data if offline.
Smithian analysis	Compares the latest market price to a moving-average proxy for Adam Smith’s “natural price”, flags over-/under-valuation, and narrates the result in plain English.
Analyst toolkit	Adds daily % returns, 20/50-day MA crossover signal, rolling 30-day volatility, and STL seasonal decomposition.
Two UIs	▸ Tkinter GUI – interactive chart embedded in the window.
▸ Rich CLI – colourised tables, JSON export, optional PNG chart save.
CI-ready repo	GitHub Actions runs black, flake8, mypy and pytest (network calls mocked) on 3.8 → 3.11.
Extensible	Stubs & TODOs for forecasting (Prophet/ARIMA), pair-symbol comparison, BigQuery ingestion, settings dialog, etc.

2 . Repository layout

smithian-price-watcher/
├── src/                       ← importable package
│   ├── price_analyzer.py      core Smithian logic
│   ├── data_generator.py      simulation fallback
│   ├── data_sources.py        live fetch + fallback abstraction
│   ├── advanced_analysis.py   returns, MA crossovers, volatility
│   ├── visualizer.py          Matplotlib helpers (GUI & CLI share)
│   └── bigquery_connector.py  (optional) GCP BigQuery wrapper
├── main.py                    Tkinter GUI entry-point
├── scripts/
│   └── smithian_price_watcher_cli.py   CLI entry-point
├── tests/                     pytest suite (network mocked)
├── .github/workflows/ci.yml   CI pipeline
├── requirements*.txt          runtime & dev deps
├── setup.py                   pip install -e .
└── docs/                      README, CHANGELOG, etc.
Every module can be opened in isolation; side-effects are guarded under
if __name__ == "__main__": or executed via entry-points.

3 . Conceptual architecture


flowchart LR
    subgraph UI
        G1[GUI:<br/>Tkinter] --- VIZ
        C1[CLI:<br/>Rich] --- VIZ
    end
    VIZ[Visualizer.py] -->|price DF| ANA
    ANA[PriceAnalyzer<br/>+ AdvancedAnal.] -->|observations| UI
    DS[DataSources] -->|price DF| ANA
    DG[DataGenerator] --> DS
    BQ[BigQueryConnector] --> DS
    subgraph Ext
        Prophet[(Forecasting)]:::todo
        Compare[(Symbol-compare)]:::todo
    end
    classDef todo fill:#fff3cd,stroke:#d39e00
Core loop: DataSources returns a tidy DataFrame ➜ PriceAnalyzer decorates it ➜ Visualizer plots ➜ UI presents both text & chart.

Plug-in points: forecasting, pairwise analysis, database back-ends. All non-blocking and flagged with # TODO: so the shipped code runs today.

4 . Installation
4.1 Prerequisites
Python ≥ 3.8.

On Linux, install build tools (python3-dev, gcc) if you plan to compile Prophet later.

4.2 Clone & install

git clone https://github.com/your-org/smithian-price-watcher.git
cd smithian-price-watcher
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt -r requirements-dev.txt
pre-commit install        # optional but recommended
4.3 Editable install (for development)

pip install -e .
5 . Using the application
5.1 GUI

smithian-gui
Enter any Yahoo Finance ticker or commodity code (CL=F, COPPER, BTC-USD, etc.).

Choose a moving-average window.

Click Analyse Prices.

Read the narrative and inspect the chart.

Use Clear Results to reset.

Offline? The GUI auto-switches to simulated data and labels the output.

5.2 CLI

# Table in the terminal
smithian-cli BZ=F --window 25

# Save a PNG chart
smithian-cli GC=F --plot

# Pipe JSON to another program
smithian-cli BTC-USD --json | jq .
Flag cheatsheet:

Flag	Default	Description
--window/-w	30	Moving-average window for “natural price” proxy
--plot	off	Export Matplotlib PNG to cwd
--json	off	Print price statistics as JSON

6 . Running & writing tests

pytest -q          # fast – <2 s (network mocked)
pytest -q -m online  # opt-in test that hits yfinance (marked xfail in CI)
Test skeletons live in tests/. When adding a feature, create a matching test file; network access should be mocked unless the test is duly marked.

7 . Continuous Integration (GitHub Actions)
CI matrix: 3.8 | 3.9 | 3.10 | 3.11 on Ubuntu.

Jobs:


black --check
flake8
mypy  (ignore-missing-imports)
pytest (cov≥80 %)
Badge turns green on every green run.

8 . Design & Coding guidelines
Guideline	Rationale
Single-responsibility modules	Easier to test & swap (e.g., switch to AlphaVantage by editing only data_sources.py).
Pure functions where feasible	advanced_analysis.* never touch UI state.
Dependency isolation	Heavy libs (Prophet, google-cloud-bigquery) are optional – import inside functions or behind try/except.
MPL figure-builder separate from Tk canvas	Allows CLI to reuse the same plots.
Type hints + mypy	CI enforces --ignore-missing-imports only, so first-party code must type-check cleanly.
Rich text for CLI, ttk for GUI	Both are std-lib or zero-config cross-platform.
All side-effects behind entry-points	Pure import never opens windows, spawns nets, or writes files.

Coding style = black default + flake8 --max-line-length 88.

9 . Contributing
Fork → branch from main.

pre-commit install (runs black/flake8/mypy automatically).

Add tests in tests/, keep network usage mocked.

Update CHANGELOG.md.

PR → GitHub Actions must be green.

See CONTRIBUTING.md for branch strategy & release cadence.

10 . Troubleshooting
Symptom	Fix
ImportError: google.cloud.bigquery	BigQuery backend is optional. Either install google-cloud-bigquery or switch data source to “Simulated”.
Empty chart / flat line	Likely the ticker has no daily data (e.g., expired contract). Try an ETF (GLD, USO).
GUI crashes on macOS with TclError	Ensure python-tk is present (brew install tcl-tk, then recreate venv with --with-tcl-tk).
CI fails mypy on Prophet stubs	Comment out Prophet in requirements.txt or run pip install prophet-stubs.

11 . Roadmap 🚧
Forecasting widget – Prophet/StatsForecast with cross-validation.

Symbol comparison – spread & correlation heatmap (--compare CLI flag, GUI tabs).

Export plugins – XLSX, PowerPoint slide, Slack webhook.

Docker image – docker run -p 8501:8501 smithian-price-watcher:latest (for Streamlit wrapper?).

Lightning⚡/Microservice – expose analysis via FastAPI endpoint.

All TODOs are tagged in code; grep # TODO: for low-hanging fruit.

12 . License
This work is released under the permissive MIT License – see LICENSE.

“Science is the great antidote to the poison of enthusiasm and superstition.”
— Adam Smith, 1759

Now go analyse some prices! 📊