"""
Unified interface: .get_price_series(symbol, days_back)
Returns DataFrame(date index, close_price).
Falls back to simulated data if network fails.
"""

from __future__ import annotations
import pandas as pd
from datetime import datetime, timedelta

import yfinance as yf
from data_generator import get_simulated_commodity_data


def _fetch_yfinance(symbol: str, days_back: int) -> pd.DataFrame:
    end = datetime.utcnow()
    start = end - timedelta(days=days_back * 2)  # grab extra – weekends
    df = (
        yf.download(symbol, start=start, end=end, progress=False)
        .reset_index()[["Date", "Close"]]
        .rename(columns={"Date": "date", "Close": "price"})
    )
    if df.empty:
        raise ValueError("empty")
    df["date"] = pd.to_datetime(df["date"])
    df = df.set_index("date").asfreq("D").ffill()
    return df.tail(days_back)


def get_price_series(symbol: str, days_back: int = 180) -> tuple[pd.DataFrame, str]:
    """
    Try yfinance first; fall back to simulated.
    Unit heuristic: $/unit for most, else empty.
    """
    try:
        df = _fetch_yfinance(symbol, days_back)
        return df, "$"
    except Exception:
        sim_df, unit = get_simulated_commodity_data(symbol, days_back)
        return sim_df, unit