import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg


def price_chart(df, symbol: str):
    fig, ax = plt.subplots(figsize=(6, 3))
    df["price"].plot(ax=ax, label="Price")
    for col in [c for c in df.columns if c.startswith("ma_")]:
        df[col].plot(ax=ax, linestyle="--", label=col.upper())
    ax.set_title(symbol)
    ax.legend()
    ax.grid(True)
    fig.tight_layout()
    return fig


def embed_in_tk(frame, fig):
    canvas = FigureCanvasTkAgg(fig, master=frame)
    canvas.draw()
    canvas.get_tk_widget().pack(fill="both", expand=True)
    return canvas