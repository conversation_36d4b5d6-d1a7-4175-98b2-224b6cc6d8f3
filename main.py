#!/usr/bin/env python3
"""
<PERSON><PERSON>er ­– GUI application
----------------------------------------

Tk-based desktop front-end that lets the user type any symbol (e.g. `GC=F`,
`BTC-USD`, `GLD`) or fall back to simulated data.  The GUI shows:

1.  A narrative “Smith<PERSON>” analysis of market vs. natural price
2.  A Matplotlib chart with price + moving averages
3.  Optional Google BigQuery connector (disabled by default)

The file is self-contained – you can run it directly after
`pip install -r requirements.txt`.
"""

from __future__ import annotations

import sys
import threading
from pathlib import Path
from typing import Optional

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext

# --------------------------------------------------------------------------- #
#  local imports (add /src to PYTHONPATH so "python main.py" works everywhere)
# --------------------------------------------------------------------------- #
SRC_PATH = Path(__file__).resolve().parent / "src"
sys.path.insert(0, str(SRC_PATH))

from price_analyzer import PriceAnalyzer
from data_generator import get_simulated_commodity_data
from bigquery_connector import BigQueryConnector  # optional; try/except inside
from visualizer import price_chart, embed_in_tk

# --------------------------------------------------------------------------- #
#  GUI class
# --------------------------------------------------------------------------- #


class SmithianPriceWatcherGUI:
    """Main window controller."""

    # --------------------------- initialise -------------------------------- #

    def __init__(self, root: tk.Tk) -> None:
        self.root = root
        self.root.title("Smithian Price Watcher – Market vs Natural Price")
        self.root.geometry("900x750")
        self.root.minsize(750, 600)

        # business objects
        self.price_analyzer = PriceAnalyzer()
        self.bigquery_connector: Optional[BigQueryConnector] = None
        self._df_latest = None  # set after every analysis

        # draw UI
        self._create_widgets()

    # --------------------------- widgets ----------------------------------- #

    def _create_widgets(self) -> None:
        """Build and grid every widget once (no dynamic layout in this app)."""
        main = ttk.Frame(self.root, padding="10")
        main.grid(sticky="nsew")
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)

        # ---- title ----
        ttk.Label(main, text="Smithian Price Watcher", font=("Arial", 18, "bold")).grid(
            row=0, column=0, columnspan=3, pady=(0, 5)
        )
        ttk.Label(
            main,
            text="“Natural price” vs market price – live or simulated",
            font=("Arial", 10, "italic"),
        ).grid(row=1, column=0, columnspan=3, pady=(0, 15))

        # ---- parameters ----
        params = ttk.LabelFrame(main, text="Analysis Parameters", padding="10")
        params.grid(row=2, column=0, columnspan=3, sticky="ew", pady=(0, 10))
        params.columnconfigure(1, weight=1)

        ttk.Label(params, text="Symbol:").grid(row=0, column=0, sticky="w")
        self.symbol_var = tk.StringVar(value="GC=F")  # gold futures
        ttk.Entry(params, textvariable=self.symbol_var, width=15).grid(
            row=0, column=1, sticky="w", padx=(0, 20)
        )

        ttk.Label(params, text="Moving-avg days:").grid(row=0, column=2, sticky="w")
        self.window_var = tk.IntVar(value=30)
        ttk.Spinbox(params, from_=7, to=120, textvariable=self.window_var, width=8).grid(
            row=0, column=3, sticky="w"
        )

        # ---- data source ----
        source = ttk.LabelFrame(main, text="Data source", padding="10")
        source.grid(row=3, column=0, columnspan=3, sticky="ew")
        self.data_src = tk.StringVar(value="simulated")
        ttk.Radiobutton(
            source,
            text="Simulated (offline demo)",
            variable=self.data_src,
            value="simulated",
        ).grid(row=0, column=0, sticky="w")
        ttk.Radiobutton(
            source,
            text="Google BigQuery (advanced)",
            variable=self.data_src,
            value="bigquery",
        ).grid(row=0, column=1, sticky="w", padx=(20, 0))

        # ---- BigQuery credentials (disabled until user selects) ----
        self.bq_frame = ttk.LabelFrame(source, text="BigQuery config", padding="5")
        self.bq_frame.grid(row=1, column=0, columnspan=2, sticky="ew", pady=(8, 0))
        self.bq_frame.columnconfigure(1, weight=1)
        self._bq_vars: dict[str, tk.StringVar] = {}
        for i, (label, key, default) in enumerate(
            [
                ("Project ID:", "project_id", ""),
                ("Dataset ID:", "dataset_id", ""),
                ("Table ID:", "table_id", ""),
                ("Date column:", "date_col", "date"),
                ("Price column:", "price_col", "price"),
                ("Commodity column:", "commodity_col", ""),
            ]
        ):
            ttk.Label(self.bq_frame, text=label).grid(row=i, column=0, sticky="w")
            sv = tk.StringVar(value=default)
            self._bq_vars[key] = sv
            ttk.Entry(self.bq_frame, textvariable=sv, width=28).grid(
                row=i, column=1, sticky="ew", pady=1
            )
        self._toggle_bq()  # start disabled
        self.data_src.trace_add("write", lambda *_: self._toggle_bq())

        # ---- buttons ----
        btns = ttk.Frame(main)
        btns.grid(row=4, column=0, columnspan=3, pady=(10, 0))
        self.btn_analyse = ttk.Button(btns, text="🔍 Analyse", command=self._kickoff)
        self.btn_analyse.grid(row=0, column=0, padx=5)
        ttk.Button(btns, text="🗑️ Clear", command=self._clear).grid(row=0, column=1, padx=5)
        ttk.Button(btns, text="❓ About", command=self._about).grid(row=0, column=2)

        # ---- chart ----
        self.chart_frame = ttk.Frame(main, height=220)
        self.chart_frame.grid(row=5, column=0, columnspan=3, sticky="nsew", pady=(15, 0))
        main.rowconfigure(5, weight=1)

        # ---- result text ----
        results = ttk.LabelFrame(main, text="Analysis narrative", padding="10")
        results.grid(row=6, column=0, columnspan=3, sticky="nsew", pady=(10, 0))
        results.columnconfigure(0, weight=1)
        results.rowconfigure(0, weight=1)
        self.txt = scrolledtext.ScrolledText(
            results, height=12, wrap="word", font=("Consolas", 10)
        )
        self.txt.grid(sticky="nsew")

        # ---- status bar ----
        self.status = tk.StringVar(value="Ready")
        ttk.Label(main, textvariable=self.status, relief="sunken").grid(
            row=7, column=0, columnspan=3, sticky="ew", pady=(8, 0)
        )

    # --------------------- BigQuery toggle ------------------------------- #

    def _toggle_bq(self) -> None:
        state = "normal" if self.data_src.get() == "bigquery" else "disabled"
        for child in self.bq_frame.winfo_children():
            if isinstance(child, ttk.Entry):
                child.configure(state=state)

    # --------------------- analysis thread entry ------------------------- #

    def _kickoff(self) -> None:
        """Disable button, spawn worker thread."""
        self.btn_analyse.config(state="disabled")
        self.status.set("Running analysis…")
        self.txt.delete(1.0, tk.END)
        threading.Thread(target=self._perform_analysis, daemon=True).start()

    # --------------------- analysis worker ------------------------------- #

    def _perform_analysis(self) -> None:
        try:
            symbol = self.symbol_var.get().strip().upper()
            window = int(self.window_var.get())

            # fetch data
            if self.data_src.get() == "simulated":
                df, unit = get_simulated_commodity_data(symbol, window + 10)
                is_sim = True
            else:  # BigQuery
                if not self._validate_bq():
                    self._safe_error("Fill in all BigQuery fields.")
                    return
                self.bigquery_connector = BigQueryConnector(
                    project_id=self._bq_vars["project_id"].get(),
                    dataset_id=self._bq_vars["dataset_id"].get(),
                    table_id=self._bq_vars["table_id"].get(),
                )
                df = self.bigquery_connector.fetch_commodity_data(
                    commodity=symbol,
                    date_column=self._bq_vars["date_col"].get(),
                    price_column=self._bq_vars["price_col"].get(),
                    commodity_column=self._bq_vars["commodity_col"].get() or None,
                    days_back=window + 60,
                )
                if df.empty:
                    self._safe_error(f"No rows returned for '{symbol}'.")
                    return
                unit = "units"
                is_sim = False

            # run analytics
            self._df_latest = df
            narrative = self.price_analyzer.analyze_prices(df, symbol, unit, window)

            # push back to GUI thread
            self.root.after(0, self._update_gui, narrative, is_sim)

        except Exception as exc:  # pylint: disable=broad-except
            self._safe_error(f"Analysis failed: {exc}")

    # --------------------- helpers --------------------------------------- #

    def _validate_bq(self) -> bool:
        required = ["project_id", "dataset_id", "table_id", "date_col", "price_col"]
        return all(self._bq_vars[key].get().strip() for key in required)

    def _safe_error(self, msg: str) -> None:
        self.root.after(0, self._show_error, msg)

    # --------------------- UI update after analysis ---------------------- #

    def _update_gui(self, narrative: str, is_sim: bool) -> None:
        # text
        self.txt.insert("end", narrative + "\n" + "=" * 60 + "\n")
        self.txt.insert(
            "end",
            "📊 Simulated data.\n" if is_sim else "📈 Live data (BigQuery).\n",
        )
        self.txt.see(1.0)

        # chart
        for child in self.chart_frame.winfo_children():
            child.destroy()
        fig = price_chart(self._df_latest, self.symbol_var.get())
        embed_in_tk(self.chart_frame, fig)

        # status
        self.status.set("Done")
        self.btn_analyse.config(state="normal")

    # --------------------- misc actions ---------------------------------- #

    def _clear(self) -> None:
        self.txt.delete(1.0, tk.END)
        for child in self.chart_frame.winfo_children():
            child.destroy()
        self.status.set("Cleared")

    def _show_error(self, msg: str) -> None:
        self.status.set("Error")
        self.btn_analyse.config(state="normal")
        self.txt.insert("end", f"❌ {msg}\n")
        messagebox.showerror("Smithian Price Watcher", msg)

    def _about(self) -> None:
        messagebox.showinfo(
            "About",
            (
                "Smithian Price Watcher v1.0\n\n"
                "Demonstrates Adam Smith’s concept of prices fluctuating around a "
                "“natural price”.\n\n"
                "GitHub: https://github.com/your-org/smithian-price-watcher"
            ),
        )


# --------------------------------------------------------------------------- #
#  entry-point
# --------------------------------------------------------------------------- #


def main() -> None:
    try:
        root = tk.Tk()
        SmithianPriceWatcherGUI(root)
        root.mainloop()
    except Exception as exc:  # pylint: disable=broad-except
        print(f"GUI failed: {exc}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()