from setuptools import setup, find_packages

with open("docs/README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

setup(
    name="smithian-price-watcher",
    version="1.0.0",
    author="<PERSON>",
    author_email="<PERSON><PERSON><PERSON><PERSON>@gmail.com",
    description="A tool for analyzing commodity prices using <PERSON>'s economic principles",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/nicholas<PERSON><PERSON>/smithian-price-watcher",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Education",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
    python_requires=">=3.8",
    install_requires=[
        "pandas>=1.5.0",
        "google-cloud-bigquery>=3.0.0",
        "pandas-gbq>=0.19.0",
    ],
    entry_points={
        "console_scripts": [
            "smithian-gui=main:main",
            "smithian-cli=scripts.smithian_price_watcher_cli:main",
        ],
    },
)
