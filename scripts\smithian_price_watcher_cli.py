#!/usr/bin/env python3
from __future__ import annotations

import argparse, sys, json, pathlib
from pathlib import Path
from rich.console import Console
from rich.table import Table

# Add src directory to Python path
SRC_PATH = Path(__file__).resolve().parent.parent / "src"
sys.path.insert(0, str(SRC_PATH))

from data_sources import get_price_series
from price_analyzer import PriceAnalyzer
from advanced_analysis import add_returns, add_moving_averages, rolling_volatility
import visualizer as viz

console = Console()


def build_parser() -> argparse.ArgumentParser:
    p = argparse.ArgumentParser(
        prog="smithian-cli",
        description="Smithian Price Watcher – Analyst Edition",
    )
    p.add_argument("symbol", help="Ticker / commodity symbol (e.g., GC=F for gold futures)")
    p.add_argument("-w", "--window", type=int, default=30, help="moving–average window")
    p.add_argument("--plot", action="store_true", help="save PNG chart to cwd")
    p.add_argument("--json", action="store_true", help="print JSON instead of table")
    return p


def main() -> None:
    args = build_parser().parse_args()
    df, unit = get_price_series(args.symbol.upper(), 200)
    df = add_returns(df)
    df = add_moving_averages(df)

    analyzer = PriceAnalyzer()
    analysis_txt = analyzer.analyze_prices(df, args.symbol, unit, args.window)

    if args.json:
        stats = analyzer.calculate_price_statistics(df)
        console.print(json.dumps(stats, indent=2))
    else:
        table = Table(title=f"{args.symbol} – last {len(df)} days")
        for k, v in analyzer.calculate_price_statistics(df).items():
            table.add_row(k, f"{v:,.2f}")
        console.print(table)
        console.rule("Narrative")
        console.print(analysis_txt)

    if args.plot:
        fig = viz.price_chart(df, args.symbol)
        fname = pathlib.Path.cwd() / f"{args.symbol}_chart.png"
        fig.savefig(fname, dpi=150)
        console.print(f"Chart saved to {fname}")


if __name__ == "__main__":
    main()