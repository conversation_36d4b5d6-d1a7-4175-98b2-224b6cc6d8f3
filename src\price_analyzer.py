"""Price analysis module for comparing market prices with natural price proxies."""

import pandas as pd
from datetime import datetime
from typing import Tuple, Optional


class PriceAnalyzer:
    """Analyzes commodity prices using <PERSON>'s market vs natural price concepts."""

    def __init__(self):
        self.threshold_percentage = 2.0  # Threshold for significant price deviation

    def analyze_prices(
        self,
        df: pd.DataFrame,
        commodity_name: str,
        unit: str,
        window_days: int = 30
    ) -> str:
        """
        Analyze commodity prices and return formatted analysis.

        Args:
            df: DataFrame with price data (indexed by date)
            commodity_name: Name of the commodity
            unit: Price unit (e.g., "/bushel", "/lb")
            window_days: Moving average window in days

        Returns:
            Formatted analysis string
        """
        if df.empty or len(df) < 1:
            return f"❌ Not enough data to analyze {commodity_name}."

        # Calculate moving average as natural price proxy
        if len(df) >= window_days:
            df = df.copy()  # Avoid modifying original DataFrame
            df['natural_price_proxy'] = df['price'].rolling(
                window=window_days, min_periods=1
            ).mean()
        else:
            df = df.copy()
            df['natural_price_proxy'] = float('nan')

        # Get latest data
        latest_data = df.iloc[-1]
        latest_date = latest_data.name

        # Extract scalar values from Series
        market_price_val = df.iloc[-1]['price']
        if hasattr(market_price_val, 'item'):
            market_price = market_price_val.item()
        else:
            market_price = float(market_price_val)

        natural_price_val = df.iloc[-1]['natural_price_proxy']
        if hasattr(natural_price_val, 'item'):
            natural_price_proxy = natural_price_val.item()
        else:
            natural_price_proxy = float(natural_price_val) if pd.notna(natural_price_val) else float('nan')

        # Format analysis
        analysis_parts = [
            f"📊 ANALYSIS: {commodity_name.upper()}",
            "=" * 50,
            f"📅 Latest Date: {self._format_date(latest_date)}",
            f"💰 Current Market Price: ${market_price:.2f} {unit}",
        ]

        if pd.notna(natural_price_proxy) and natural_price_proxy > 0:
            analysis_parts.append(
                f"📈 {window_days}-Day Average (Natural Price Proxy): ${natural_price_proxy:.2f} {unit}"
            )
            analysis_parts.append("")  # Empty line

            # Calculate and interpret price deviation
            price_diff_percentage = (
                (market_price - natural_price_proxy) / natural_price_proxy
            ) * 100

            observation = self._generate_observation(
                commodity_name, price_diff_percentage, window_days
            )
            analysis_parts.append(observation)
        else:
            analysis_parts.append(
                f"📈 {window_days}-Day Average: Insufficient data for full window"
            )

        # Add Smithian connection
        analysis_parts.extend([
            "",
            "🎓 SMITHIAN CONNECTION:",
            "Adam Smith's 'The Wealth of Nations' (Book I, Ch. VII) explains that",
            "market prices fluctuate around a 'natural price' based on production costs",
            "and ordinary profits. Deviations from this natural price signal market",
            "imbalances that eventually self-correct through supply and demand adjustments.",
            "",
            "This analysis uses a moving average as a proxy for Smith's 'natural price'",
            "concept, helping identify when current market conditions might be creating",
            "temporary price distortions."
        ])

        return "\n".join(analysis_parts)

    def _format_date(self, date_obj) -> str:
        """Format date object for display."""
        if isinstance(date_obj, str):
            return date_obj
        elif isinstance(date_obj, datetime):
            return date_obj.strftime('%Y-%m-%d')
        elif hasattr(date_obj, 'strftime'):
            return date_obj.strftime('%Y-%m-%d')
        else:
            return str(date_obj)

    def _generate_observation(
        self, commodity_name: str, price_diff_percentage: float, window_days: int
    ) -> str:
        """Generate price deviation observation."""
        if price_diff_percentage > self.threshold_percentage:
            return (
                f"🔍 OBSERVATION: Current price is {price_diff_percentage:.2f}% "
                f"ABOVE its {window_days}-day average.\n"
                f"💡 This could indicate: increased demand, supply constraints, "
                f"speculative activity, or seasonal factors affecting {commodity_name}."
            )
        elif price_diff_percentage < -self.threshold_percentage:
            return (
                f"🔍 OBSERVATION: Current price is {abs(price_diff_percentage):.2f}% "
                f"BELOW its {window_days}-day average.\n"
                f"💡 This might suggest: oversupply, reduced demand, market corrections, "
                f"or other downward pressures on {commodity_name}."
            )
        else:
            return (
                f"🔍 OBSERVATION: Current price is relatively ALIGNED with its "
                f"{window_days}-day average (±{abs(price_diff_percentage):.2f}%).\n"
                f"💡 This suggests {commodity_name} is trading near its recent equilibrium."
            )

    def calculate_price_statistics(self, df: pd.DataFrame) -> dict:
        """Calculate basic price statistics."""
        if df.empty:
            return {}

        # Convert pandas values to Python scalars
        min_price = df['price'].min()
        max_price = df['price'].max()
        mean_price = df['price'].mean()
        std_price = df['price'].std()
        latest_price = df.iloc[-1]['price']
        price_change = df.iloc[-1]['price'] - df.iloc[0]['price'] if len(df) > 1 else 0

        return {
            'min_price': min_price.item() if hasattr(min_price, 'item') else float(min_price),
            'max_price': max_price.item() if hasattr(max_price, 'item') else float(max_price),
            'mean_price': mean_price.item() if hasattr(mean_price, 'item') else float(mean_price),
            'std_price': std_price.item() if hasattr(std_price, 'item') else float(std_price),
            'latest_price': latest_price.item() if hasattr(latest_price, 'item') else float(latest_price),
            'price_change': price_change.item() if hasattr(price_change, 'item') else float(price_change),
        }
