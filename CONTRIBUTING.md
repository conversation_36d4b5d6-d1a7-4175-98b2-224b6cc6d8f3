# Contributing to **<PERSON><PERSON>er**

🎉 **Thank you for taking the time to contribute!**  
Whether you spotted a one-line typo or plan to add a forecasting engine, all pull requests are welcome.  
The rules below keep the codebase clean, tests green, and releases predictable.

---

## 1  Getting started

### 1.1  Fork & clone

```bash
git clone https://github.com/<your-user>/smithian-price-watcher.git
cd smithian-price-watcher
git remote add upstream https://github.com/your-org/smithian-price-watcher.git
1.2 Python env

python -m venv venv
source venv/bin/activate
pip install -r requirements.txt -r requirements-dev.txt
pre-commit install          # hooks run automatically on commit
1.3 Run the test suite

Edit
pytest -q
You should see all tests pass (✓).

2 Branching & workflow
Branch	Purpose	Protected?
main	Always deployable; latest release tag	yes
dev/*	Feature or bug-fix branches	no
release/*	Pre-release integr. testing	yes

Never commit to main directly.

Start every PR from dev/my-feature (or a descriptive name).

3 Coding guidelines
Rule	Tool / Setting
Black format	black . (line-length 88)
Flake8 clean	flake8 . --max-line-length 88 --extend-ignore E203,W503
Static types	mypy --ignore-missing-imports src
Tests	pytest with ≥ 80 % coverage (pytest.ini enforces)
Docstrings	Google style ("""Summary.\n\nArgs:\n ...""")
Commit style	Conventional Commits (fix:, feat:, docs: …)

Tip pre-commit runs Black, Flake8 and Mypy automatically.
Fix issues locally before you push—CI will reject non-conforming code.

4 Writing tests
Place tests in tests/ with file names test_*.py.

Network calls must be mocked (see tests/test_data_sources.py).

Use pytest.mark.xfail for optional online tests—CI is offline.

Example:


from unittest.mock import patch
from data_sources import get_price_series

@patch("data_sources._fetch_yfinance")
def test_fallback_simulation(mock_fetch):
    mock_fetch.side_effect = Exception("offline")
    df, unit = get_price_series("WHEAT", 30)
    assert not df.empty and unit
5 Commit messages

type(scope): subject

body (optional)
type: feat, fix, docs, refactor, test, build, ci, chore

scope: file or module (e.g. price_analyzer)

Use imperative mood: “add rolling volatility” not “added”.

6 Pull-request checklist
Tests added/updated and pass pytest.

CHANGELOG.md entry under “Unreleased → Added/Changed/Fixes”.

Docs updated if public API changes (docs/README.md).

pre-commit run --all-files is clean.

CI badge on the PR is green.

7 Issue triage
Bug – include steps to reproduce, expected vs actual output, stack trace.

Feature – explain user problem, not just solution idea.

Question/Support – please use GitHub Discussions tab.

Label legend:

Label	Meaning
good first issue	≤ 15 lines, no API break, perfect for newcomers
help wanted	Core team short on bandwidth
blocked	Waiting on external bug / dependency

8 Security
If you discover a security vulnerability do not create a public issue.
Email <EMAIL> with details; we follow a 90-day disclosure window.

9 Code of conduct
This project follows the Contributor Covenant v2.1.
Be kind. Disagreement ≠ disparagement.

10 License & copyright
All contributions are licensed under the MIT License; by submitting a PR you agree to release your code under the same terms.

Happy hacking! 🚀