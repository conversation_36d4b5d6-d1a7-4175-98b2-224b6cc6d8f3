"""
Top-level package for Smithian Price Watcher.

Re-exports:
    ▸ PriceAnalyzer – core business logic
    ▸ get_simulated_commodity_data – quick offline demo data
    ▸ BigQueryConnector – optional live-data backend
"""

from .price_analyzer import PriceAnalyzer
from .data_generator import get_simulated_commodity_data
from .bigquery_connector import BigQueryConnector  # noqa: F401 (imported for users)

__all__: list[str] = [
    "PriceAnalyzer",
    "get_simulated_commodity_data",
    # BigQueryConnector is intentionally *not* in __all__; advanced users can
    # still import it via `from smithian_price_watcher import BigQueryConnector`
]

__version__: str = "1.0.0"