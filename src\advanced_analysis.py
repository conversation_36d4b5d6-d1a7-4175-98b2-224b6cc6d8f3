from __future__ import annotations
import pandas as pd
from statsmodels.tsa.seasonal import STL


def add_returns(df: pd.DataFrame) -> pd.DataFrame:
    df["return_pct"] = df["price"].pct_change() * 100
    return df


def add_moving_averages(df: pd.DataFrame, fast: int = 20, slow: int = 50) -> pd.DataFrame:
    df[f"ma_{fast}"] = df["price"].rolling(fast).mean()
    df[f"ma_{slow}"] = df["price"].rolling(slow).mean()
    df["ma_signal"] = (df[f"ma_{fast}"] > df[f"ma_{slow}"]).astype(int)
    return df


def rolling_volatility(df: pd.DataFrame, window: int = 30) -> pd.Series:
    return df["return_pct"].rolling(window).std()


def seasonal_decompose(df: pd.DataFrame) -> STL:
    return STL(df["price"], period=7).fit()  # weekly seasonality