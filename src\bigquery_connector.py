"""
Thin wrapper around google-cloud-bigquery so we can
(1) swap in a stub for unit tests; (2) keep the GUI/CLI tidy.
"""

from __future__ import annotations

from datetime import datetime, timedelta
from typing import Optional

import pandas as pd

try:
    # Import lazily – not installed in CI matrix
    from google.cloud import bigquery  # type: ignore
except ModuleNotFoundError:  # pragma: no cover
    bigquery = None  # allows graceful degradation


class BigQueryConnector:
    """Fetches daily commodity prices from a BigQuery table."""

    def __init__(self, project_id: str, dataset_id: str, table_id: str) -> None:
        if bigquery is None:  # pragma: no cover
            raise ImportError(
                "google-cloud-bigquery not found.  "
                "Install it or run in simulated-data mode."
            )

        self.project_id = project_id
        self.dataset_id = dataset_id
        self.table_id = table_id
        self.client = bigquery.Client(project=project_id)

    # --------------------------------------------------------------------- #
    def fetch_commodity_data(
        self,
        *,
        commodity: str,
        date_column: str,
        price_column: str,
        commodity_column: Optional[str] = None,
        days_back: int = 60,
    ) -> pd.DataFrame:
        """
        Return a DataFrame (date index, 'price' col) of the *latest* `days_back`
        observations.  Empty DF if nothing returned.
        """

        where_clause = ""
        if commodity_column:
            escaped_commodity = commodity.replace("'", "\\'")
            where_clause = f"WHERE {commodity_column} = '{escaped_commodity}'"

        sql = f"""
        WITH recent AS (
            SELECT
              CAST({date_column} AS DATE)  AS dt,
              CAST({price_column} AS NUMERIC) AS price
            FROM `{self.project_id}.{self.dataset_id}.{self.table_id}`
            {where_clause}
            ORDER BY dt DESC
            LIMIT {days_back}
        )
        SELECT * FROM recent ORDER BY dt ASC
        """

        df = self.client.query(sql).result().to_dataframe()
        if df.empty:
            return df

        df["dt"] = pd.to_datetime(df["dt"])
        df = df.set_index("dt")
        df.index.name = "date"
        return df