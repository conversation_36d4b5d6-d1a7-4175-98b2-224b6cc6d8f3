from unittest.mock import MagicMock, patch

import pandas as pd

# patch google.cloud.bigquery before import
with patch.dict("sys.modules", {"google.cloud.bigquery": MagicMock()}):
    from bigquery_connector import BigQueryConnector


def test_fetch_returns_empty_when_no_rows():
    mock_client = MagicMock()
    mock_client.query.return_value.result.return_value.to_dataframe.return_value = pd.DataFrame()

    with patch("bigquery_connector.bigquery.Client", return_value=mock_client):
        conn = BigQueryConnector("proj", "ds", "tbl")
        df = conn.fetch_commodity_data(
            commodity="WHEAT",
            date_column="date",
            price_column="price",
            commodity_column="commodity",
        )
        assert df.empty