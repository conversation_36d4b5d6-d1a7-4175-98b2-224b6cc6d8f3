"""Data generation module for creating simulated commodity price data."""

import pandas as pd
import random
from datetime import datetime, timed<PERSON><PERSON>
from typing import <PERSON>ple


def get_simulated_commodity_data(
    commodity_name: str, num_days: int = 35
) -> Tuple[pd.DataFrame, str]:
    """
    Generate simulated daily prices for a commodity.
    
    Args:
        commodity_name: Name of the commodity to simulate
        num_days: Number of days of historical data to generate
        
    Returns:
        Tuple of (DataFrame with price data, unit string)
    """
    # Set commodity-specific parameters
    commodity_params = {
        'WHEAT': {'base_price': 6.50, 'volatility': 0.03, 'unit': '/bushel'},
        'COFFEE': {'base_price': 1.80, 'volatility': 0.07, 'unit': '/lb'},
        'OIL': {'base_price': 75.00, 'volatility': 0.04, 'unit': '/barrel'},
        'CORN': {'base_price': 5.20, 'volatility': 0.035, 'unit': '/bushel'},
        'GOLD': {'base_price': 1950.00, 'volatility': 0.02, 'unit': '/oz'},
        'SILVER': {'base_price': 24.50, 'volatility': 0.05, 'unit': '/oz'},
    }
    
    params = commodity_params.get(
        commodity_name.upper(), 
        {'base_price': 10.00, 'volatility': 0.05, 'unit': '/units'}
    )
    
    base_price = params['base_price']
    volatility_factor = params['volatility']
    unit = params['unit']
    
    # Generate price data
    prices = []
    current_date = datetime.now()
    
    # Set random seed for reproducible "random" data in tests
    random.seed(hash(commodity_name) % 2**32)
    
    for i in range(num_days):
        date_obj = current_date - timedelta(days=i)
        
        # Create some trend patterns
        # Older data has slight trend, recent data has some volatility
        days_from_now = i
        trend_factor = 1 + (days_from_now * 0.0005 * random.choice([-1, 1]))
        
        # Add daily price noise
        daily_noise = (random.random() - 0.5) * (base_price * volatility_factor * 2)
        
        # Calculate simulated price
        sim_price = base_price * trend_factor + daily_noise
        
        # Add some recent volatility for the last few days
        if i < 5:
            recent_volatility = (5 - i) * 0.01 * base_price * random.choice([-1, 1])
            sim_price += recent_volatility
        
        # Ensure price stays positive
        sim_price = max(sim_price, base_price * 0.1)
        
        prices.append({
            'date': date_obj.strftime('%Y-%m-%d'),
            'price': round(sim_price, 2)
        })
    
    # Create DataFrame sorted by date (ascending)
    df = pd.DataFrame(sorted(prices, key=lambda x: x['date']))
    df['date'] = pd.to_datetime(df['date'])
    df = df.set_index('date')
    
    return df, unit


def add_price_noise(df: pd.DataFrame, noise_factor: float = 0.02) -> pd.DataFrame:
    """Add random noise to existing price data."""
    df = df.copy()
    noise = df['price'] * noise_factor * (pd.Series([random.random() - 0.5